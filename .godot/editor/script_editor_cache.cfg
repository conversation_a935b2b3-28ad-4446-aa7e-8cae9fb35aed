[res://scripts/VirtualKeyboard.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 139,
"scroll_position": 127.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/ReversedMessagePuzzle.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 30,
"scroll_position": 18.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/MainMenu.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 175,
"scroll_position": 163.0,
"selection": false,
"syntax_highlighter": "GDScript"
}
