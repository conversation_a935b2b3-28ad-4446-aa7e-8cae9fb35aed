extends Control
class_name NavigationPuzzle

signal puzzle_solved
signal puzzle_failed

@onready var puzzle_panel: NinePatchRect = $PuzzlePanel
@onready var title_label: Label = $PuzzlePanel/VBoxContainer/TitleLabel
@onready var description_label: RichTextLabel = $PuzzlePanel/VBoxContainer/DescriptionLabel
@onready var step_label: Label = $PuzzlePanel/VBoxContainer/StepLabel
@onready var direction_container: GridContainer = $PuzzlePanel/VBoxContainer/DirectionContainer
@onready var north_button: TextureButton = $PuzzlePanel/VBoxContainer/DirectionContainer/NorthButton
@onready var north_label: Label = $PuzzlePanel/VBoxContainer/DirectionContainer/NorthButton/NorthLabel
@onready var west_button: TextureButton = $PuzzlePanel/VBoxContainer/DirectionContainer/WestButton
@onready var west_label: Label = $PuzzlePanel/VBoxContainer/DirectionContainer/WestButton/WestLabel
@onready var center_label: Label = $PuzzlePanel/VBoxContainer/DirectionContainer/CenterLabel
@onready var east_button: TextureButton = $PuzzlePanel/VBoxContainer/DirectionContainer/EastButton
@onready var east_label: Label = $PuzzlePanel/VBoxContainer/DirectionContainer/EastButton/EastLabel
@onready var south_button: TextureButton = $PuzzlePanel/VBoxContainer/DirectionContainer/SouthButton
@onready var south_label: Label = $PuzzlePanel/VBoxContainer/DirectionContainer/SouthButton/SouthLabel
@onready var progress_label: Label = $PuzzlePanel/VBoxContainer/ProgressLabel
@onready var hint_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/HintButton
@onready var reset_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/ResetButton
@onready var close_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton

var correct_sequence: Array[String] = ["V", "Z", "Z", "S"]  # Východ, Západ, Západ, Sever
var current_sequence: Array[String] = []
var current_step: int = 0
var hint_level: int = 0
var max_hints: int = 3

var step_descriptions: Array[String] = [
	"Stojíte na križovatke lesných ciest. Kam pôjdete?",
	"Pokračujete lesom. Ďalšia križovatka sa objavuje pred vami.",
	"Hmla sa zahusťuje. Musíte si vybrať správny smer.",
	"Posledná križovatka. Zámok by mal byť už blízko..."
]

func _ready():
	hide()
	
	# Pripojenie signálov
	if north_button:
		north_button.pressed.connect(_on_direction_pressed.bind("S"))
	if south_button:
		south_button.pressed.connect(_on_direction_pressed.bind("J"))
	if east_button:
		east_button.pressed.connect(_on_direction_pressed.bind("V"))
	if west_button:
		west_button.pressed.connect(_on_direction_pressed.bind("Z"))
	
	if hint_button:
		hint_button.pressed.connect(_on_hint_pressed)
	if reset_button:
		reset_button.pressed.connect(_on_reset_pressed)
	if close_button:
		close_button.pressed.connect(_on_close_pressed)
	
	setup_puzzle()

func setup_puzzle():
	if title_label:
		title_label.text = "Cesta lesom"
	
	if description_label:
		description_label.text = "[center]Cesta sa rozdeľuje na štyri smery.[/center]\n\n[b]Van Helsingova poznámka:[/b]\n'Východ, potom dvakrát na západ, nakoniec sever - tak sa dostaneš k zámku.'\n\nVyberte správny smer:"
	
	# Nastavenie tlačidiel
	if north_label:
		north_label.text = "↑\nSEVER"
	if south_label:
		south_label.text = "↓\nJUH"
	if east_label:
		east_label.text = "→\nVÝCHOD"
	if west_label:
		west_label.text = "←\nZÁPAD"
	if center_label:
		center_label.text = "🧭"
	
	reset_puzzle()

func show_puzzle():
	show()
	update_display()

func _on_direction_pressed(direction: String):
	current_sequence.append(direction)
	current_step += 1
	
	# Kontrola správnosti kroku
	if current_step <= correct_sequence.size():
		if direction == correct_sequence[current_step - 1]:
			# Správny krok
			if current_step >= correct_sequence.size():
				# Hlavolam vyriešený
				AudioManager.play_puzzle_success_sound()
				puzzle_solved.emit()
				hide()
			else:
				# Pokračovanie na ďalší krok
				update_display()
		else:
			# Nesprávny krok
			AudioManager.play_puzzle_error_sound()
			show_error_feedback()
			await get_tree().create_timer(1.0).timeout
			reset_puzzle()

func update_display():
	if step_label and current_step < step_descriptions.size():
		step_label.text = step_descriptions[current_step]
	
	if progress_label:
		var progress_text = "Kroky: "
		for i in range(current_sequence.size()):
			progress_text += get_direction_symbol(current_sequence[i])
			if i < current_sequence.size() - 1:
				progress_text += " → "
		
		if current_step < correct_sequence.size():
			progress_text += " → ?"
		
		progress_label.text = progress_text

func get_direction_symbol(direction: String) -> String:
	match direction:
		"S": return "↑"
		"J": return "↓"
		"V": return "→"
		"Z": return "←"
		_: return "?"

func show_error_feedback():
	# Červené zablikanie tlačidiel
	var buttons = [north_button, south_button, east_button, west_button]
	for button in buttons:
		if button:
			button.modulate = Color.RED
	
	var tween = create_tween()
	tween.parallel().tween_property(north_button, "modulate", Color.WHITE, 0.5)
	tween.parallel().tween_property(south_button, "modulate", Color.WHITE, 0.5)
	tween.parallel().tween_property(east_button, "modulate", Color.WHITE, 0.5)
	tween.parallel().tween_property(west_button, "modulate", Color.WHITE, 0.5)

func _on_hint_pressed():
	print("🔍 NavigationPuzzle: Hint button pressed!")
	hint_level += 1

	if hint_level <= max_hints:
		var hint_text = get_hint_text(hint_level)
		show_simple_hint_dialog(hint_text)
	else:
		show_simple_hint_dialog("Už ste použili všetky nápovedy!")

func get_hint_text(level: int) -> String:
	match level:
		1:
			return "Sledujte presne poradie v poznámke."
		2:
			return "Prvý smer je východ (→)."
		3:
			return "Celé poradie: Východ → Západ → Západ → Sever"
		_:
			return "Už ste použili všetky nápovedy!"

func show_simple_hint_dialog(hint_text: String):
	# Vytvorenie responzívneho hint dialógu
	var hint_overlay = ColorRect.new()
	hint_overlay.color = Color(0, 0, 0, 0.8)
	hint_overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	add_child(hint_overlay)

	var hint_panel = NinePatchRect.new()
	hint_panel.texture = load("res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Big_Panel.png")
	hint_panel.patch_margin_left = 25
	hint_panel.patch_margin_top = 25
	hint_panel.patch_margin_right = 25
	hint_panel.patch_margin_bottom = 25
	# Responzívna veľkosť - max 80% šírky obrazovky, min 300px
	var screen_size = get_viewport().get_visible_rect().size
	var panel_width = min(max(300, screen_size.x * 0.8), 500)
	var panel_height = min(max(150, screen_size.y * 0.4), 300)
	hint_panel.size = Vector2(panel_width, panel_height)

	# Manuálne centrovanie
	hint_panel.position = Vector2(
		(screen_size.x - panel_width) / 2,
		(screen_size.y - panel_height) / 2
	)
	hint_overlay.add_child(hint_panel)

	# VBoxContainer pre obsah
	var content_vbox = VBoxContainer.new()
	content_vbox.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	content_vbox.add_theme_constant_override("separation", 15)
	hint_panel.add_child(content_vbox)

	# Margin container
	var margin_container = MarginContainer.new()
	margin_container.add_theme_constant_override("margin_left", 20)
	margin_container.add_theme_constant_override("margin_right", 20)
	margin_container.add_theme_constant_override("margin_top", 20)
	margin_container.add_theme_constant_override("margin_bottom", 20)
	content_vbox.add_child(margin_container)

	var inner_vbox = VBoxContainer.new()
	inner_vbox.add_theme_constant_override("separation", 10)
	margin_container.add_child(inner_vbox)

	# Titulok
	var title_label = Label.new()
	title_label.text = "💡 Nápoveda"
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.add_theme_font_size_override("font_size", 20)
	title_label.add_theme_color_override("font_color", Color(0.9, 0.8, 0.6, 1))
	inner_vbox.add_child(title_label)

	# Text nápovedy s automatickým zalamovaním
	var hint_label = RichTextLabel.new()
	hint_label.bbcode_enabled = true
	hint_label.text = "[center]" + hint_text + "[/center]"
	hint_label.fit_content = true
	hint_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	hint_label.size_flags_vertical = Control.SIZE_EXPAND_FILL
	hint_label.custom_minimum_size = Vector2(0, 60)
	inner_vbox.add_child(hint_label)

	# Tlačidlo OK
	var ok_button = Button.new()
	ok_button.text = "OK"
	ok_button.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
	ok_button.custom_minimum_size = Vector2(100, 40)
	inner_vbox.add_child(ok_button)

	# Pripojenie signálu na zatvorenie
	ok_button.pressed.connect(func(): hint_overlay.queue_free())

	# Možnosť zatvoriť kliknutím na overlay
	hint_overlay.gui_input.connect(func(event):
		if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
			hint_overlay.queue_free()
	)

func _on_reset_pressed():
	reset_puzzle()

func reset_puzzle():
	current_sequence.clear()
	current_step = 0
	update_display()

func _on_close_pressed():
	puzzle_failed.emit()
	hide()



func _input(event):
	if visible and event.is_action_pressed("ui_cancel"):
		_on_close_pressed()
