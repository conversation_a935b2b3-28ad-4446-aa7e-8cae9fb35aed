extends Node

# Test script pre overenie funkčnosti NewChaptersMenu

func _ready():
	print("🧪 Testovanie NewChaptersMenu...")
	
	# Test načítania scény
	var scene_path = "res://scenes/NewChaptersMenu.tscn"
	
	if FileAccess.file_exists(scene_path):
		print("✅ Scéna existuje: ", scene_path)
		
		var scene_resource = load(scene_path)
		if scene_resource:
			print("✅ Scéna sa načítala úspešne")
			
			var scene_instance = scene_resource.instantiate()
			if scene_instance:
				print("✅ Inštancia sa vytvorila úspešne")
				
				# Test uzlov
				test_scene_nodes(scene_instance)
				
				scene_instance.queue_free()
			else:
				print("❌ Nepodarilo sa vytvoriť inštanciu")
		else:
			print("❌ Nepodarilo sa načítať scénu")
	else:
		print("❌ Scéna neexistuje: ", scene_path)
	
	# Test GameManager funkcií
	test_game_manager()
	
	print("🧪 Test dokončený")

func test_scene_nodes(scene_instance):
	print("🔍 Testovanie uzlov v scéne...")
	
	var required_nodes = [
		"MainPanel/TitleLabel",
		"MainPanel/MainContainer/ChapterInfoContainer/InfoPanel/InfoContent/ChapterTitle",
		"MainPanel/MainContainer/ChapterInfoContainer/InfoPanel/InfoContent/ChapterDescription",
		"MainPanel/MainContainer/ChapterImageContainer/CenterContainer/ChapterImage",
		"MainPanel/ButtonsContainer/PlayButton",
		"MainPanel/ButtonsContainer/BackButton"
	]
	
	for node_path in required_nodes:
		var node = scene_instance.get_node_or_null(node_path)
		if node:
			print("  ✅ ", node_path, " - OK")
		else:
			print("  ❌ ", node_path, " - CHÝBA!")

func test_game_manager():
	print("🎮 Testovanie GameManager...")
	
	if GameManager:
		print("✅ GameManager je dostupný")
		print("  - Aktuálna kapitola: ", GameManager.current_chapter)
		print("  - Posledná hraná kapitola: ", GameManager.last_played_chapter)
	else:
		print("❌ GameManager nie je dostupný")
