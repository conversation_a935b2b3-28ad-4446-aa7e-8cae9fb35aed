extends Control
class_name OrderTestPuzzle

signal puzzle_solved
signal puzzle_failed

@onready var puzzle_panel: NinePatchRect = $PuzzlePanel
@onready var title_label: Label = $PuzzlePanel/VBoxContainer/TitleLabel
@onready var description_label: RichTextLabel = $PuzzlePanel/VBoxContainer/DescriptionLabel
@onready var question_label: Label = $PuzzlePanel/VBoxContainer/QuestionLabel
@onready var answer_field: LineEdit = $PuzzlePanel/VBoxContainer/AnswerContainer/AnswerField
@onready var submit_button: TextureButton = $PuzzlePanel/VBoxContainer/AnswerContainer/SubmitButton
@onready var progress_label: Label = $PuzzlePanel/VBoxContainer/ProgressLabel
@onready var hint_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/HintButton
@onready var close_button: TextureButton = $PuzzlePanel/VBoxContainer/ButtonContainer/CloseButton


var questions: Array[Dictionary] = [
	{
		"question": "Ak<PERSON> kov sa používal v alchýmii na čistenie duše a zároveň poškodzuje telo nemŕtvych?",
		"answer": "STRIEBRO",
		"alternatives": ["STRIEBRO", "SILVER"]
	},
	{
		"question": "Ktoré nebeské teleso alchymisti spájali so striebrom a ktoré ovláda príliv a odliv?",
		"answer": "MESIAC",
		"alternatives": ["MESIAC", "LUNA", "MOON"]
	},
	{
		"question": "Aký bylinný výťažok sa podľa legiend prikladal na hroby, aby zadržal prekliatych pod zemou?",
		"answer": "CESNAK",
		"alternatives": ["CESNAK", "GARLIC"]
	}
]

var current_question: int = 0
var hint_level: int = 0
var max_hints: int = 3

func _ready():
	hide()
	
	# Pripojenie signálov
	if submit_button:
		submit_button.pressed.connect(_on_submit_pressed)
	if hint_button:
		hint_button.pressed.connect(_on_hint_pressed)
	if close_button:
		close_button.pressed.connect(_on_close_pressed)
	if answer_field:
		answer_field.text_submitted.connect(_on_text_submitted)
	
	setup_puzzle()

func setup_puzzle():
	if title_label:
		title_label.text = "Skúška Rádu"
	
	if description_label:
		description_label.text = "[center][b]Viktor:[/b] Tri otázky. Tri odpovede.[/center]\n\nKaždá z iného sveta – kov, hviezda, bylina.\nDokážte, že patríte k Rádu:"
	
	current_question = 0
	display_current_question()

func show_puzzle():
	show()
	if answer_field:
		answer_field.grab_focus()

func display_current_question():
	if current_question < questions.size():
		var question_data = questions[current_question]
		
		if question_label:
			question_label.text = "Otázka " + str(current_question + 1) + ":\n" + question_data.question
		
		if progress_label:
			progress_label.text = "Otázka " + str(current_question + 1) + " z " + str(questions.size())
		
		if answer_field:
			answer_field.text = ""
			answer_field.placeholder_text = "Zadajte odpoveď..."
			answer_field.grab_focus()

func _on_submit_pressed():
	check_answer()

func _on_text_submitted(text: String):
	check_answer()

func check_answer():
	if not answer_field or current_question >= questions.size():
		return
	
	var user_answer = normalize_text(answer_field.text)
	var question_data = questions[current_question]
	
	# Kontrola správnej odpovede
	var is_correct = false
	for alternative in question_data.alternatives:
		if user_answer == normalize_text(alternative):
			is_correct = true
			break
	
	if is_correct:
		# Správna odpoveď
		AudioManager.play_puzzle_success_sound()
		current_question += 1

		if current_question >= questions.size():
			# Všetky otázky zodpovedané
			puzzle_solved.emit()
			hide()
		else:
			# Ďalšia otázka
			show_success_feedback()
			await get_tree().create_timer(1.0).timeout
			display_current_question()
	else:
		# Nesprávna odpoveď
		AudioManager.play_puzzle_error_sound()
		show_error_feedback()

func normalize_text(text: String) -> String:
	# Odstránenie diakritiky a normalizácia
	var normalized = text.to_upper().strip_edges()
	
	# Základná náhrada diakritiky
	var replacements = {
		"Á": "A", "Ä": "A", "Č": "C", "Ď": "D", "É": "E", "Ě": "E",
		"Í": "I", "Ľ": "L", "Ĺ": "L", "Ň": "N", "Ó": "O", "Ô": "O",
		"Ŕ": "R", "Š": "S", "Ť": "T", "Ú": "U", "Ů": "U", "Ý": "Y",
		"Ž": "Z"
	}
	
	for old_char in replacements:
		normalized = normalized.replace(old_char, replacements[old_char])
	
	return normalized

func show_success_feedback():
	if answer_field:
		answer_field.modulate = Color.GREEN
		var tween = create_tween()
		tween.tween_property(answer_field, "modulate", Color.WHITE, 1.0)

func show_error_feedback():
	if answer_field:
		answer_field.modulate = Color.RED
		var tween = create_tween()
		tween.tween_property(answer_field, "modulate", Color.WHITE, 0.5)

func _on_hint_pressed():
	hint_level += 1
	
	if hint_level <= max_hints:
		var hint_text = get_hint_text(hint_level)
		show_simple_hint_dialog(hint_text)
	else:
		show_simple_hint_dialog("Už ste použili všetky nápovedy!")

func get_hint_text(level: int) -> String:
	match level:
		1:
			return "Tri svety - kov, hviezda, bylina. Každý má svoju moc proti temnote."
		2:
			return "Kov čistí duše, hviezda ovláda vody, bylina chráni hroby."
		3:
			return "Striebro, Mesiac, Cesnak - základy každého lovca upírov."
		_:
			return "Už ste použili všetky nápovedy!"

func show_simple_hint_dialog(hint_text: String):
	# Vytvorenie responzívneho hint dialógu
	var hint_overlay = ColorRect.new()
	hint_overlay.color = Color(0, 0, 0, 0.8)
	hint_overlay.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	add_child(hint_overlay)

	var hint_panel = NinePatchRect.new()
	hint_panel.texture = load("res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Big_Panel.png")
	hint_panel.patch_margin_left = 25
	hint_panel.patch_margin_top = 25
	hint_panel.patch_margin_right = 25
	hint_panel.patch_margin_bottom = 25
	# Responzívna veľkosť - max 80% šírky obrazovky, min 300px
	var screen_size = get_viewport().get_visible_rect().size
	var panel_width = min(max(300, screen_size.x * 0.8), 500)
	var panel_height = min(max(150, screen_size.y * 0.4), 300)
	hint_panel.size = Vector2(panel_width, panel_height)

	# Manuálne centrovanie
	hint_panel.position = Vector2(
		(screen_size.x - panel_width) / 2,
		(screen_size.y - panel_height) / 2
	)
	hint_overlay.add_child(hint_panel)

	# VBoxContainer pre obsah
	var content_vbox = VBoxContainer.new()
	content_vbox.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
	content_vbox.add_theme_constant_override("separation", 15)
	hint_panel.add_child(content_vbox)

	# Margin container
	var margin_container = MarginContainer.new()
	margin_container.add_theme_constant_override("margin_left", 20)
	margin_container.add_theme_constant_override("margin_right", 20)
	margin_container.add_theme_constant_override("margin_top", 20)
	margin_container.add_theme_constant_override("margin_bottom", 20)
	content_vbox.add_child(margin_container)

	var inner_vbox = VBoxContainer.new()
	inner_vbox.add_theme_constant_override("separation", 10)
	margin_container.add_child(inner_vbox)

	# Titulok
	var title_label = Label.new()
	title_label.text = "💡 Nápoveda"
	title_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	title_label.add_theme_font_size_override("font_size", 20)
	title_label.add_theme_color_override("font_color", Color(0.9, 0.8, 0.6, 1))
	inner_vbox.add_child(title_label)

	# Text nápovedy s automatickým zalamovaním
	var hint_label = RichTextLabel.new()
	hint_label.bbcode_enabled = true
	hint_label.text = "[center]" + hint_text + "[/center]"
	hint_label.fit_content = true
	hint_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	hint_label.size_flags_vertical = Control.SIZE_EXPAND_FILL
	hint_label.custom_minimum_size = Vector2(0, 60)
	inner_vbox.add_child(hint_label)

	# Tlačidlo OK
	var ok_button = Button.new()
	ok_button.text = "OK"
	ok_button.size_flags_horizontal = Control.SIZE_SHRINK_CENTER
	ok_button.custom_minimum_size = Vector2(100, 40)
	inner_vbox.add_child(ok_button)

	# Pripojenie signálu na zatvorenie
	ok_button.pressed.connect(func(): hint_overlay.queue_free())

	# Možnosť zatvoriť kliknutím na overlay
	hint_overlay.gui_input.connect(func(event):
		if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
			hint_overlay.queue_free()
	)

func _on_close_pressed():
	puzzle_failed.emit()
	hide()

func _input(event):
	if visible and event.is_action_pressed("ui_cancel"):
		_on_close_pressed()
